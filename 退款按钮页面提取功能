// ==UserScript==
// @name         微信小商店售后信息提取器 - 极速预加载版
// @namespace    http://tampermonkey.net/
// @version      4.0
// @description  通过后台预加载和缓存机制，实现点击按钮后零延迟提取最终协商历史记录的完整核心信息。
// <AUTHOR>
// @match        https://store.weixin.qq.com/shop/aftersale/detail?orderid=*
// @grant        GM_addStyle
// @license      MIT
// ==/UserScript==

(function() {
    'use strict';

    // --- 变量定义 ---
    let extractedInfo = null; // 用于缓存预提取的数据
    let extractionInProgress = true; // 标记后台提取是否仍在进行

    // --- 样式注入 ---
    GM_addStyle(`
        #info-panel {
            position: fixed;
            top: 150px;
            right: 20px;
            width: 300px;
            background-color: white;
            border: 1px solid #dcdcdc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            color: #333;
        }
        #info-panel-header {
            padding: 10px;
            cursor: move;
            background-color: #f7f7f7;
            border-bottom: 1px solid #dcdcdc;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
        }
        #info-panel-title {
            font-weight: bold;
        }
        #info-panel-toggle {
            cursor: pointer;
            padding: 2px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 12px;
        }
        #info-panel-content {
            padding: 10px;
            display: block;
        }
        #info-panel.collapsed #info-panel-content {
            display: none;
        }
        #extract-button {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            background-color: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        #extract-button:hover {
            background-color: #06ad56;
        }
        #info-display {
            width: 100%;
            height: 200px;
            box-sizing: border-box;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 12px;
            line-height: 1.6;
            resize: vertical;
        }
    `);

    // --- UI创建与交互 ---
    const panel = document.createElement('div');
    panel.id = 'info-panel';
    panel.innerHTML = `
        <div id="info-panel-header">
            <span id="info-panel-title">信息提取面板</span>
            <span id="info-panel-toggle">收起</span>
        </div>
        <div id="info-panel-content">
            <button id="extract-button">提取信息</button>
            <textarea id="info-display"></textarea>
        </div>
    `;
    document.body.appendChild(panel);

    const infoPanel = document.querySelector('#info-panel');
    const panelHeader = document.querySelector('#info-panel-header');
    const toggleButton = document.querySelector('#info-panel-toggle');
    const extractButton = document.querySelector('#extract-button');
    const infoDisplay = document.querySelector('#info-display');

    toggleButton.addEventListener('click', () => {
        infoPanel.classList.toggle('collapsed');
        toggleButton.textContent = infoPanel.classList.contains('collapsed') ? '展开' : '收起';
    });

    let isDragging = false;
    let offsetX, offsetY;
    panelHeader.addEventListener('mousedown', (e) => {
        isDragging = true;
        offsetX = e.clientX - infoPanel.offsetLeft;
        offsetY = e.clientY - infoPanel.offsetTop;
        panelHeader.style.cursor = 'grabbing';
    });
    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        infoPanel.style.left = `${e.clientX - offsetX}px`;
        infoPanel.style.top = `${e.clientY - offsetY}px`;
    });
    document.addEventListener('mouseup', () => {
        isDragging = false;
        panelHeader.style.cursor = 'move';
    });

    // --- 后台预提取逻辑 ---
    function startProactiveExtraction() {
        const maxAttempts = 50; // 10秒超时
        const intervalTime = 200;
        let attempts = 0;

        const intervalId = setInterval(() => {
            attempts++;
            if (attempts >= maxAttempts) {
                clearInterval(intervalId);
                extractedInfo = "错误：查找超时。\n无法定位目标元素。\n请确认页面已完全加载或刷新重试。";
                extractionInProgress = false;
                return;
            }

            const microAppHost = document.querySelector('micro-app[name="aftersale"]');
            const shadowRoot = microAppHost ? microAppHost.shadowRoot : null;
            if (!shadowRoot) return;

            const firstStepItem = shadowRoot.querySelector('.step .step-item:first-child');
            if (!firstStepItem) return;

            // --- 目标已找到，停止轮询并缓存数据 ---
            clearInterval(intervalId);

            const getText = (selector) => {
                const element = firstStepItem.querySelector(selector);
                return element ? element.innerText.trim() : '未找到';
            };

            const findDetailByLabel = (label) => {
                const detailItems = firstStepItem.querySelectorAll('.block-item');
                for (const item of detailItems) {
                    const titleElement = item.querySelector('.block-title');
                    if (titleElement && titleElement.innerText.trim() === label) {
                        const contentElement = item.querySelector('.block-content');
                        return contentElement ? contentElement.innerText.trim() : '未找到内容';
                    }
                }
                return '未找到标签';
            };

            const status = getText('.body .title');
            const processingInfo = getText('.body .tips');
            const refundAmount = findDetailByLabel('退款金额');
            const refundDestination = findDetailByLabel('钱款去向');

            extractedInfo = `  - 订单售后状态：${status}
  - 退款时间信息：${processingInfo}
  - 退款金额与去向：${refundAmount} ${refundDestination}`;

            extractionInProgress = false;
        }, intervalTime);
    }

    // --- 按钮点击事件（即时响应） ---
    extractButton.addEventListener('click', () => {
        if (extractionInProgress) {
            infoDisplay.value = "正在后台查找，请稍候...";
        } else {
            infoDisplay.value = extractedInfo;
        }
    });

    // --- 启动后台提取 ---
    startProactiveExtraction();

})();
